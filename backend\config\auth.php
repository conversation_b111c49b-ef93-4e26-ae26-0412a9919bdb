<?php

class Auth {
    private static $instance = null;
    private $user = null;
    private $sessionName = 'user_session';
    
    private function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            $lifetime = getenv('SESSION_LIFETIME') ?: 120;
            $secure = filter_var(getenv('SESSION_SECURE') ?: false, FILTER_VALIDATE_BOOLEAN);
            
            session_set_cookie_params([
                'lifetime' => $lifetime * 60,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => true,
                'samesite' => 'Strict'
            ]);
            
            session_name('irego_session');
            session_start();
            
            // Regenerate session ID periodically to prevent session fixation
            if (!isset($_SESSION['created_at']) || time() - $_SESSION['created_at'] > 1800) {
                session_regenerate_id(true);
                $_SESSION['created_at'] = time();
            }
        }
        
        // Check if user is logged in
        if (isset($_SESSION[$this->sessionName])) {
            $this->user = $_SESSION[$this->sessionName];
        }
    }
    
    // Prevent cloning of the instance
    private function __clone() {}
    
    // Prevent unserialization of the instance
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function login($user) {
        $this->user = $user;
        $_SESSION[$this->sessionName] = $user;
        // Regenerate session ID on login to prevent session fixation
        session_regenerate_id(true);
        return true;
    }
    
    public function logout() {
        $this->user = null;
        unset($_SESSION[$this->sessionName]);
        session_regenerate_id(true);
        return true;
    }
    
    public function isLoggedIn() {
        return $this->user !== null;
    }
    
    public function getUser() {
        return $this->user;
    }
    
    public function getUserId() {
        return $this->user ? $this->user['id'] : null;
    }
    
    public function hasRole($role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        return $this->user['role'] === $role;
    }
}