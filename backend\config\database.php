<?php

class Database {
    private static $instance = null;
    private $connection;
    private $queryCount = 0;
    private $queryLog = [];
    private $transactionLevel = 0;
    
    private function __construct() {
        $host = getenv('DB_HOST') ?: 'localhost';
        $dbname = getenv('DB_NAME') ?: 'irego_db';
        $username = getenv('DB_USER') ?: 'root';
        $password = getenv('DB_PASS') ?: '';
        $port = getenv('DB_PORT') ?: '3306';
        
        try {
            $this->connection = new PDO(
                "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
                $username,
                $password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_FOUND_ROWS => true,
                    PDO::ATTR_TIMEOUT => 5, // 5 seconds timeout
                ]
            );
            
            // Set session SQL mode for better security and compatibility
            $this->connection->exec("SET SESSION sql_mode = 'STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'");
            
        } catch (PDOException $e) {
            // Log error instead of exposing details
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed. Please check your configuration.");
        }
    }
    
    // Prevent cloning of the instance
    private function __clone() {}
    
    // Prevent unserialization of the instance
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function prepare($sql) {
        $this->logQuery($sql);
        return $this->connection->prepare($sql);
    }
    
    public function query($sql, $params = []) {
        $this->logQuery($sql);
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    public function execute($sql, $params = []) {
        $this->logQuery($sql);
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute($params);
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    public function beginTransaction() {
        if ($this->transactionLevel == 0) {
            $this->connection->beginTransaction();
        }
        $this->transactionLevel++;
        return true;
    }
    
    public function commit() {
        if ($this->transactionLevel == 1) {
            $this->connection->commit();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
        return true;
    }
    
    public function rollBack() {
        if ($this->transactionLevel == 1) {
            $this->connection->rollBack();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
        return true;
    }
    
    // إضافة دوال جديدة للأمان والأداء
    
    private function logQuery($sql) {
        $this->queryCount++;
        if (getenv('APP_DEBUG') == 'true') {
            $this->queryLog[] = [
                'query' => $sql,
                'time' => microtime(true)
            ];
        }
    }
    
    public function getQueryCount() {
        return $this->queryCount;
    }
    
    public function getQueryLog() {
        return $this->queryLog;
    }
    
    public function escape($value) {
        return $this->connection->quote($value);
    }
    
    public function tableExists($table) {
        $result = $this->query("SHOW TABLES LIKE ?", [$table]);
        return $result->rowCount() > 0;
    }
    
    public function getServerInfo() {
        return [
            'version' => $this->connection->getAttribute(PDO::ATTR_SERVER_VERSION),
            'info' => $this->connection->getAttribute(PDO::ATTR_SERVER_INFO),
            'connection_status' => $this->connection->getAttribute(PDO::ATTR_CONNECTION_STATUS)
        ];
    }
    
    public function checkConnection() {
        try {
            $this->connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
}