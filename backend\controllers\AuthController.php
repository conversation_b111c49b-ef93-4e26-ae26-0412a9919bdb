<?php

require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../helpers/validation.php';
require_once __DIR__ . '/../helpers/response.php';
require_once __DIR__ . '/../helpers/security.php';

class AuthController {
    private $userModel;
    private $auth;
    
    public function __construct() {
        $this->userModel = new User();
        $this->auth = Auth::getInstance();
    }
    
    public function login($data) {
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'email' => 'required|email',
            'password' => 'required|min:8'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Find user by email
        $user = $this->userModel->findByEmail($data['email']);
        
        if (!$user) {
            // Use generic error message to prevent user enumeration
            return Response::error('Invalid credentials', [], 401);
        }
        
        // Check if user is active
        if (!$user['is_active']) {
            return Response::error('Account is inactive', [], 403);
        }
        
        // Verify password
        if (!$this->userModel->verifyPassword($data['password'], $user['password'])) {
            // Use generic error message to prevent user enumeration
            return Response::error('Invalid credentials', [], 401);
        }
        
        // Remove password from user data
        unset($user['password']);
        
        // Update last login timestamp
        $this->userModel->updateLastLogin($user['id']);
        
        // Login user
        $this->auth->login($user);
        
        // Generate JWT token for API access
        $token = Security::generateJWT($user);
        
        return Response::success('Login successful', [
            'user' => $user,
            'token' => $token
        ]);
    }
    
    public function logout() {
        $this->auth->logout();
        return Response::success('Logout successful');
    }
    
    public function register($data) {
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'username' => 'required|alpha_num|min:3|max:50',
            'email' => 'required|email',
            'password' => 'required|min:8',
            'password_confirm' => 'required|same:password',
            'first_name' => 'required|alpha|max:50',
            'last_name' => 'required|alpha|max:50'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Check if email already exists
        $existingUser = $this->userModel->findByEmail($data['email']);
        if ($existingUser) {
            return Response::error('Email already in use', [], 409);
        }
        
        // Check if username already exists
        $existingUser = $this->userModel->findByUsername($data['username']);
        if ($existingUser) {
            return Response::error('Username already in use', [], 409);
        }
        
        // Create user
        $userId = $this->userModel->create([
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => $data['password'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'role' => 'user'
        ]);
        
        if (!$userId) {
            return Response::error('Failed to create user', [], 500);
        }
        
        // Get user data without password
        $user = $this->userModel->findById($userId);
        
        return Response::success('User registered successfully', ['user' => $user], 201);
    }
    
    public function getCurrentUser() {
        if (!$this->auth->isLoggedIn()) {
            return Response::error('Not authenticated', [], 401);
        }
        
        $user = $this->auth->getUser();
        
        return Response::success('User retrieved successfully', ['user' => $user]);
    }
    
    public function updateProfile($data) {
        if (!$this->auth->isLoggedIn()) {
            return Response::error('Not authenticated', [], 401);
        }
        
        $userId = $this->auth->getUserId();
        
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'first_name' => 'alpha|max:50',
            'last_name' => 'alpha|max:50',
            'email' => 'email',
            'current_password' => 'required_with:password',
            'password' => 'min:8',
            'password_confirm' => 'same:password'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // If email is being changed, check if it's already in use
        if (isset($data['email'])) {
            $existingUser = $this->userModel->findByEmail($data['email']);
            if ($existingUser && $existingUser['id'] != $userId) {
                return Response::error('Email already in use', [], 409);
            }
        }
        
        // If password is being changed, verify current password
        if (isset($data['password'])) {
            $user = $this->userModel->findById($userId);
            if (!$this->userModel->verifyPassword($data['current_password'], $user['password'])) {
                return Response::error('Current password is incorrect', [], 401);
            }
            
            // Remove current_password and password_confirm from data
            unset($data['current_password']);
            unset($data['password_confirm']);
        }
        
        // Update user
        $success = $this->userModel->update($userId, $data);
        
        if (!$success) {
            return Response::error('Failed to update profile', [], 500);
        }
        
        // Get updated user data
        $user = $this->userModel->findById($userId);
        
        // Update session with new user data
        $this->auth->login($user);
        
        return Response::success('Profile updated successfully', ['user' => $user]);
    }
}