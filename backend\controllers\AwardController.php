<?php

require_once __DIR__ . '/../models/Award.php';
require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../helpers/validation.php';
require_once __DIR__ . '/../helpers/response.php';
require_once __DIR__ . '/../helpers/security.php';

class AwardController {
    private $awardModel;
    private $auth;
    
    public function __construct() {
        $this->awardModel = new Award();
        $this->auth = Auth::getInstance();
    }
    
    public function getAwards() {
        $awards = $this->awardModel->getActiveAwards();
        
        return Response::success('Awards retrieved successfully', ['awards' => $awards]);
    }
    
    public function getAward($id) {
        $award = $this->awardModel->findById($id);
        
        if (!$award) {
            return Response::error('Award not found', [], 404);
        }
        
        // Get nominees for this award
        $nominees = $this->awardModel->getNominees($id);
        $award['nominees'] = $nominees;
        
        return Response::success('Award retrieved successfully', ['award' => $award]);
    }
    
    public function vote($data) {
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'award_id' => 'required|integer',
            'nominee_id' => 'required|integer',
            'email' => 'required|email'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Check if award exists and is active
        $award = $this->awardModel->findById($data['award_id']);
        if (!$award || !$award['is_active']) {
            return Response::error('Award not found or inactive', [], 404);
        }
        
        // Check if nominee exists and belongs to this award
        $nominee = $this->awardModel->findNomineeById($data['nominee_id']);
        if (!$nominee || $nominee['award_id'] != $data['award_id']) {
            return Response::error('Nominee not found or does not belong to this award', [], 404);
        }
        
        // Check if user already voted for this award
        $userId = $this->auth->isLoggedIn() ? $this->auth->getUserId() : null;
        $hasVoted = $this->awardModel->hasVoted($data['award_id'], $data['email'], $_SERVER['REMOTE_ADDR']);
        
        if ($hasVoted) {
            return Response::error('You have already voted for this award', [], 409);
        }
        
        // Record vote
        $voteId = $this->awardModel->vote([
            'award_id' => $data['award_id'],
            'nominee_id' => $data['nominee_id'],
            'user_id' => $userId,
            'email' => $data['email'],
            'ip_address' => $_SERVER['REMOTE_ADDR']
        ]);
        
        if (!$voteId) {
            return Response::error('Failed to record vote', [], 500);
        }
        
        // Increment nominee votes count
        $this->awardModel->incrementVotes($data['nominee_id']);
        
        return Response::success('Vote recorded successfully');
    }
    
    public function getResults($awardId) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        $award = $this->awardModel->findById($awardId);
        
        if (!$award) {
            return Response::error('Award not found', [], 404);
        }
        
        // Get nominees with vote counts
        $nominees = $this->awardModel->getNomineesWithVotes($awardId);
        
        return Response::success('Results retrieved successfully', [
            'award' => $award,
            'nominees' => $nominees
        ]);
    }
}