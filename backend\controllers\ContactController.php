<?php

require_once __DIR__ . '/../models/Message.php';
require_once __DIR__ . '/../helpers/validation.php';
require_once __DIR__ . '/../helpers/response.php';
require_once __DIR__ . '/../helpers/security.php';

class ContactController {
    private $messageModel;
    
    public function __construct() {
        $this->messageModel = new Message();
    }
    
    public function send($data) {
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'name' => 'required|max:100',
            'email' => 'required|email',
            'subject' => 'required|max:255',
            'message' => 'required'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Sanitize input to prevent XSS
        $data = Security::sanitizeInput($data);
        
        // Create message
        $messageId = $this->messageModel->create([
            'name' => $data['name'],
            'email' => $data['email'],
            'subject' => $data['subject'],
            'message' => $data['message'],
            'ip_address' => $_SERVER['REMOTE_ADDR']
        ]);
        
        if (!$messageId) {
            return Response::error('Failed to send message', [], 500);
        }
        
        // TODO: Send notification email to admin
        
        return Response::success('Message sent successfully', [], 201);
    }
    
    public function getMessages($options = []) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        $messages = $this->messageModel->getAll($options);
        
        return Response::success('Messages retrieved successfully', ['messages' => $messages]);
    }
    
    public function getMessage($id) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        $message = $this->messageModel->findById($id);
        
        if (!$message) {
            return Response::error('Message not found', [], 404);
        }
        
        // Mark message as read
        $this->messageModel->markAsRead($id);
        
        return Response::success('Message retrieved successfully', ['message' => $message]);
    }
}