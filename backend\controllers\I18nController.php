<?php

require_once __DIR__ . '/../models/Settings.php';
require_once __DIR__ . '/../helpers/response.php';

class I18nController {
    private $settingsModel;
    private $supportedLanguages = ['ar', 'en', 'fr'];
    private $defaultLanguage = 'ar';
    
    public function __construct() {
        $this->settingsModel = new Settings();
    }
    
    public function getTranslations($data = [], $params = []) {
        // Get language from route parameter
        $lang = $params['lang'] ?? $this->defaultLanguage;
        
        // Validate language
        if (!in_array($lang, $this->supportedLanguages)) {
            return Response::error('Unsupported language', [], 400);
        }
        
        // Get translations from settings
        $translationKey = 'translations_' . $lang;
        $translations = $this->settingsModel->getByKey($translationKey);
        
        if (!$translations) {
            // If translations not found, try to load from file
            $translationsFile = __DIR__ . '/../resources/lang/' . $lang . '.json';
            
            if (file_exists($translationsFile)) {
                $translations = json_decode(file_get_contents($translationsFile), true);
                
                // Store translations in settings for faster access next time
                $this->settingsModel->set($translationKey, json_encode($translations), true);
            } else {
                // If file not found, return empty translations
                $translations = [];
            }
        } else {
            // Parse JSON from settings
            $translations = json_decode($translations, true);
        }
        
        // Get public settings
        $settings = $this->settingsModel->getAllPublic();
        
        // Combine translations and settings
        $response = [
            'translations' => $translations,
            'settings' => $settings
        ];
        
        return Response::success('Translations retrieved successfully', $response);
    }
    
    public function updateTranslations($lang, $data) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        // Validate language
        if (!in_array($lang, $this->supportedLanguages)) {
            return Response::error('Unsupported language', [], 400);
        }
        
        // Validate input
        if (!isset($data['translations']) || !is_array($data['translations'])) {
            return Response::error('Invalid translations data', [], 422);
        }
        
        // Update translations in settings
        $translationKey = 'translations_' . $lang;
        $success = $this->settingsModel->set($translationKey, json_encode($data['translations']), true);
        
        if (!$success) {
            return Response::error('Failed to update translations', [], 500);
        }
        
        return Response::success('Translations updated successfully');
    }
    
    public function getSupportedLanguages() {
        return Response::success('Supported languages retrieved successfully', [
            'languages' => $this->supportedLanguages,
            'default' => $this->defaultLanguage
        ]);
    }
}