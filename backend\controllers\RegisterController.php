<?php

require_once __DIR__ . '/../models/Registration.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../helpers/validation.php';
require_once __DIR__ . '/../helpers/response.php';

class RegisterController {
    private $registrationModel;
    private $userModel;
    private $auth;
    
    public function __construct() {
        $this->registrationModel = new Registration();
        $this->userModel = new User();
        $this->auth = Auth::getInstance();
    }
    
    public function submit($data) {
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'first_name' => 'required|alpha|max:50',
            'last_name' => 'required|alpha|max:50',
            'email' => 'required|email',
            'phone' => 'required',
            'organization' => 'required|max:100',
            'position' => 'required|max:100',
            'registration_type' => 'required|in:regular,student,speaker,sponsor'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Check if email already registered
        $existingRegistration = $this->registrationModel->findByEmail($data['email']);
        if ($existingRegistration) {
            return Response::error('Email already registered', [], 409);
        }
        
        // Set user_id if logged in
        $userId = null;
        if ($this->auth->isLoggedIn()) {
            $userId = $this->auth->getUserId();
        }
        
        // Create registration
        $registrationData = [
            'user_id' => $userId,
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'organization' => $data['organization'],
            'position' => $data['position'],
            'registration_type' => $data['registration_type'],
            'payment_status' => 'pending',
            'is_confirmed' => false,
            'notes' => $data['notes'] ?? null
        ];
        
        // Set amount based on registration type
        switch ($data['registration_type']) {
            case 'regular':
                $registrationData['amount'] = 100.00;
                break;
            case 'student':
                $registrationData['amount'] = 50.00;
                break;
            case 'speaker':
                $registrationData['amount'] = 0.00;
                break;
            case 'sponsor':
                $registrationData['amount'] = 500.00;
                break;
        }
        
        $registrationId = $this->registrationModel->create($registrationData);
        
        if (!$registrationId) {
            return Response::error('Failed to create registration', [], 500);
        }
        
        // Get registration data
        $registration = $this->registrationModel->findById($registrationId);
        
        // TODO: Send confirmation email with confirmation code
        
        return Response::success('Registration submitted successfully', [
            'registration' => $registration,
            'confirmation_code' => $registration['confirmation_code']
        ], 201);
    }
    
    public function confirm($code) {
        // Find registration by confirmation code
        $registration = $this->registrationModel->findByConfirmationCode($code);
        
        if (!$registration) {
            return Response::error('Invalid confirmation code', [], 404);
        }
        
        // Check if already confirmed
        if ($registration['is_confirmed']) {
            return Response::success('Registration already confirmed', ['registration' => $registration]);
        }
        
        // Confirm registration
        $success = $this->registrationModel->confirm($registration['id']);
        
        if (!$success) {
            return Response::error('Failed to confirm registration', [], 500);
        }
        
        // Get updated registration data
        $registration = $this->registrationModel->findById($registration['id']);
        
        return Response::success('Registration confirmed successfully', ['registration' => $registration]);
    }
    
    public function getRegistration($id) {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            return Response::error('Not authenticated', [], 401);
        }
        
        // Get registration
        $registration = $this->registrationModel->findById($id);
        
        if (!$registration) {
            return Response::error('Registration not found', [], 404);
        }
        
        // Check if user has access to this registration
        $userId = $this->auth->getUserId();
        $isAdmin = $this->auth->hasRole('admin');
        
        if (!$isAdmin && $registration['user_id'] != $userId) {
            return Response::error('Unauthorized', [], 403);
        }
        
        return Response::success('Registration retrieved successfully', ['registration' => $registration]);
    }
}