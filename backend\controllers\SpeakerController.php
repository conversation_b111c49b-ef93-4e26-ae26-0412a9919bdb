<?php

require_once __DIR__ . '/../models/Speaker.php';
require_once __DIR__ . '/../helpers/response.php';
require_once __DIR__ . '/../helpers/security.php';

class SpeakerController {
    private $speakerModel;
    
    public function __construct() {
        $this->speakerModel = new Speaker();
    }
    
    public function getSpeakers($data = [], $params = []) {
        // Parse query parameters for filtering and pagination
        $options = [];
        
        // Get query parameters from URL
        $queryParams = $_GET;
        
        // Set up filters
        $filters = [];
        
        if (isset($queryParams['keynote']) && $queryParams['keynote'] === 'true') {
            $filters['is_keynote'] = true;
        }
        
        if (isset($queryParams['featured']) && $queryParams['featured'] === 'true') {
            $filters['is_featured'] = true;
        }
        
        if (!empty($filters)) {
            $options['filters'] = $filters;
        }
        
        // Set up pagination
        if (isset($queryParams['limit']) && is_numeric($queryParams['limit'])) {
            $options['limit'] = (int) $queryParams['limit'];
        }
        
        if (isset($queryParams['offset']) && is_numeric($queryParams['offset'])) {
            $options['offset'] = (int) $queryParams['offset'];
        }
        
        // Set up ordering
        if (isset($queryParams['order_by'])) {
            $allowedOrderFields = ['first_name', 'last_name', 'organization'];
            if (in_array($queryParams['order_by'], $allowedOrderFields)) {
                $options['order_by'] = $queryParams['order_by'];
            }
        }
        
        if (isset($queryParams['order_direction'])) {
            $allowedDirections = ['ASC', 'DESC'];
            if (in_array(strtoupper($queryParams['order_direction']), $allowedDirections)) {
                $options['order_direction'] = strtoupper($queryParams['order_direction']);
            }
        }
        
        // Get speakers
        $speakers = $this->speakerModel->getAll($options);
        
        // Sanitize output data
        foreach ($speakers as &$speaker) {
            // Remove sensitive information
            unset($speaker['email']);
            
            // Format photo URL if it exists
            if (!empty($speaker['photo'])) {
                $speaker['photo'] = getenv('APP_URL') . '/uploads/speakers/' . $speaker['photo'];
            }
        }
        
        return Response::success('Speakers retrieved successfully', ['speakers' => $speakers]);
    }
    
    public function getSpeaker($id) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        $speaker = $this->speakerModel->findById($id);
        
        if (!$speaker) {
            return Response::error('Speaker not found', [], 404);
        }
        
        return Response::success('Speaker retrieved successfully', ['speaker' => $speaker]);
    }
    
    public function create($data) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'first_name' => 'required|alpha|max:50',
            'last_name' => 'required|alpha|max:50',
            'email' => 'required|email',
            'organization' => 'required|max:100',
            'position' => 'required|max:100'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Check if email already exists
        $existingSpeaker = $this->speakerModel->findByEmail($data['email']);
        if ($existingSpeaker) {
            return Response::error('Email already in use', [], 409);
        }
        
        // Create speaker
        $speakerId = $this->speakerModel->create($data);
        
        if (!$speakerId) {
            return Response::error('Failed to create speaker', [], 500);
        }
        
        // Get speaker data
        $speaker = $this->speakerModel->findById($speakerId);
        
        return Response::success('Speaker created successfully', ['speaker' => $speaker], 201);
    }
    
    public function update($id, $data) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        // Check if speaker exists
        $speaker = $this->speakerModel->findById($id);
        if (!$speaker) {
            return Response::error('Speaker not found', [], 404);
        }
        
        // Validate input
        $validator = new Validator();
        $validator->validate($data, [
            'first_name' => 'alpha|max:50',
            'last_name' => 'alpha|max:50',
            'email' => 'email',
            'organization' => 'max:100',
            'position' => 'max:100'
        ]);
        
        if ($validator->hasErrors()) {
            return Response::error('Validation failed', $validator->getErrors(), 422);
        }
        
        // Check if email already exists (if email is being updated)
        if (isset($data['email']) && $data['email'] !== $speaker['email']) {
            $existingSpeaker = $this->speakerModel->findByEmail($data['email']);
            if ($existingSpeaker) {
                return Response::error('Email already in use', [], 409);
            }
        }
        
        // Update speaker
        $success = $this->speakerModel->update($id, $data);
        
        if (!$success) {
            return Response::error('Failed to update speaker', [], 500);
        }
        
        // Get updated speaker data
        $speaker = $this->speakerModel->findById($id);
        
        return Response::success('Speaker updated successfully', ['speaker' => $speaker]);
    }
    
    public function delete($id) {
        // This method should be protected and only accessible by admins
        // The access control should be handled in the API endpoint
        
        // Check if speaker exists
        $speaker = $this->speakerModel->findById($id);
        if (!$speaker) {
            return Response::error('Speaker not found', [], 404);
        }
        
        // Delete speaker
        $success = $this->speakerModel->delete($id);
        
        if (!$success) {
            return Response::error('Failed to delete speaker', [], 500);
        }
        
        return Response::success('Speaker deleted successfully');
    }
}