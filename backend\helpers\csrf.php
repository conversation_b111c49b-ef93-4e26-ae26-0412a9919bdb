<?php

require_once __DIR__ . '/security.php';

class CSRF {
    public static function generateToken() {
        return Security::generateCSRFToken();
    }
    
    public static function verifyToken($token) {
        return Security::verifyCSRFToken($token);
    }
    
    public static function getTokenField() {
        $token = self::generateToken();
        return "<input type=\"hidden\" name=\"csrf_token\" value=\"{$token}\">";
    }
    
    public static function validateRequest() {
        // Check if request method is POST, PUT, DELETE, PATCH
        $method = $_SERVER['REQUEST_METHOD'];
        if (!in_array($method, ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            return true;
        }
        
        // Get token from request
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
        
        if (!$token) {
            return false;
        }
        
        return self::verifyToken($token);
    }
}