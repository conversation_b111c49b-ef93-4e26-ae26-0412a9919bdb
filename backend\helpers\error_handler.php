<?php

require_once __DIR__ . '/response.php';

class ErrorHandler {
    public static function register() {
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleShutdown']);
    }
    
    public static function handleError($errno, $errstr, $errfile, $errline) {
        if (!(error_reporting() & $errno)) {
            // This error code is not included in error_reporting
            return false;
        }
        
        $error = [
            'type' => $errno,
            'message' => $errstr,
            'file' => $errfile,
            'line' => $errline
        ];
        
        self::logError($error);
        
        if (getenv('APP_ENV') === 'production') {
            echo Response::error('An error occurred', [], 500);
            exit(1);
        }
        
        return false;
    }
    
    public static function handleException($exception) {
        $error = [
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ];
        
        self::logError($error);
        
        if (getenv('APP_ENV') === 'production') {
            echo Response::error('An error occurred', [], 500);
        } else {
            echo Response::error($exception->getMessage(), [
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ], 500);
        }
        
        exit(1);
    }
    
    public static function handleShutdown() {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            self::logError($error);
            
            if (getenv('APP_ENV') === 'production') {
                echo Response::error('An error occurred', [], 500);
            } else {
                echo Response::error($error['message'], [
                    'file' => $error['file'],
                    'line' => $error['line']
                ], 500);
            }
        }
    }
    
    private static function logError($error) {
        $logDir = __DIR__ . '/../logs';
        
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/error.log';
        $timestamp = date('Y-m-d H:i:s');
        
        $message = "[$timestamp] {$error['type']}: {$error['message']} in {$error['file']} on line {$error['line']}\n";
        
        if (isset($error['trace'])) {
            $message .= "Trace: {$error['trace']}\n";
        }
        
        file_put_contents($logFile, $message, FILE_APPEND);
    }
}