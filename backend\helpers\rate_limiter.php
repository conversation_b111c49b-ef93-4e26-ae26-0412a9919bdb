<?php

require_once __DIR__ . '/../models/Settings.php';
require_once __DIR__ . '/response.php';

class RateLimiter {
    private $settings;
    private $maxRequests = 60; // الحد الأقصى للطلبات في الدقيقة
    private $windowSeconds = 60; // فترة النافذة الزمنية بالثواني
    
    public function __construct() {
        $this->settings = new Settings();
    }
    
    public function check($identifier = null) {
        if (!$identifier) {
            $identifier = $this->getClientIdentifier();
        }
        
        $key = 'rate_limit:' . $identifier;
        $requests = $this->getRequestCount($key);
        
        if ($requests >= $this->maxRequests) {
            header('Retry-After: ' . $this->windowSeconds);
            echo Response::error('Too many requests. Please try again later.', [], 429);
            exit();
        }
        
        $this->incrementRequestCount($key);
        return true;
    }
    
    private function getClientIdentifier() {
        return md5($_SERVER['REMOTE_ADDR'] . $_SERVER['HTTP_USER_AGENT']);
    }
    
    private function getRequestCount($key) {
        $data = $this->settings->get($key);
        
        if (!$data) {
            return 0;
        }
        
        $data = json_decode($data, true);
        
        if ($data['expires'] < time()) {
            return 0;
        }
        
        return $data['count'];
    }
    
    private function incrementRequestCount($key) {
        $data = $this->settings->get($key);
        
        if (!$data) {
            $data = [
                'count' => 1,
                'expires' => time() + $this->windowSeconds
            ];
        } else {
            $data = json_decode($data, true);
            
            if ($data['expires'] < time()) {
                $data = [
                    'count' => 1,
                    'expires' => time() + $this->windowSeconds
                ];
            } else {
                $data['count']++;
            }
        }
        
        $this->settings->set($key, json_encode($data), false);
    }
}