<?php

class Security {
    private static $jwtSecret;
    private static $jwtExpiration = 1800; // تقليل المدة إلى 30 دقيقة
    
    public static function init() {
        self::$jwtSecret = getenv('APP_SECRET') ?: 'a8e7d6f5c4b3a2e1d0f9c8b7a6e5d4c3b2a1f0e9';
    }
    
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = self::sanitizeInput($value);
            }
        } else {
            // Remove HTML tags and encode special characters
            $data = htmlspecialchars(strip_tags($data), ENT_QUOTES, 'UTF-8');
        }
        
        return $data;
    }
    
    public static function generateJWT($user) {
        $issuedAt = time();
        $expirationTime = $issuedAt + self::$jwtExpiration;
        
        $payload = [
            'iat' => $issuedAt,
            'exp' => $expirationTime,
            'nbf' => $issuedAt, // Not Before
            'iss' => getenv('APP_URL'), // Issuer
            'jti' => bin2hex(random_bytes(16)), // JWT ID
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role']
        ];
        
        return self::encodeJWT($payload);
    }
    
    public static function verifyJWT($token) {
        try {
            $payload = self::decodeJWT($token);
            
            // Check if token is expired
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return false;
            }
            
            return $payload;
        } catch (Exception $e) {
            return false;
        }
    }
    
    private static function encodeJWT($payload) {
        // Create header
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $header = self::base64UrlEncode($header);
        
        // Create payload
        $payload = json_encode($payload);
        $payload = self::base64UrlEncode($payload);
        
        // Create signature
        $signature = hash_hmac('sha256', $header . '.' . $payload, self::$jwtSecret, true);
        $signature = self::base64UrlEncode($signature);
        
        return $header . '.' . $payload . '.' . $signature;
    }
    
    private static function decodeJWT($token) {
        list($header, $payload, $signature) = explode('.', $token);
        
        // Verify signature
        $calculatedSignature = hash_hmac('sha256', $header . '.' . $payload, self::$jwtSecret, true);
        $calculatedSignature = self::base64UrlEncode($calculatedSignature);
        
        if ($signature !== $calculatedSignature) {
            throw new Exception('Invalid signature');
        }
        
        // Decode payload
        $payload = json_decode(self::base64UrlDecode($payload), true);
        
        return $payload;
    }
    
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    private static function base64UrlDecode($data) {
        return base64_decode(strtr($data, '-_', '+/') . str_repeat('=', 3 - (3 + strlen($data)) % 4));
    }
    
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    public static function verifyCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
            return false;
        }
        
        return true;
    }
}

// Initialize Security class
Security::init();