<?php

class Validator {
    private $errors = [];
    
    public function validate($data, $rules) {
        foreach ($rules as $field => $fieldRules) {
            $fieldRules = explode('|', $fieldRules);
            
            foreach ($fieldRules as $rule) {
                // Check if rule has parameters
                if (strpos($rule, ':') !== false) {
                    list($ruleName, $ruleParam) = explode(':', $rule, 2);
                } else {
                    $ruleName = $rule;
                    $ruleParam = null;
                }
                
                // Skip validation if field is not required and is empty
                if ($ruleName !== 'required' && !isset($data[$field]) && empty($data[$field])) {
                    continue;
                }
                
                // Apply validation rule
                $method = 'validate' . ucfirst($ruleName);
                if (method_exists($this, $method)) {
                    $this->$method($field, $data[$field] ?? null, $ruleParam, $data);
                }
            }
        }
        
        return empty($this->errors);
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    private function validateRequired($field, $value, $param, $data) {
        if (!isset($data[$field]) || $value === '' || $value === null) {
            $this->errors[$field][] = "The {$field} field is required.";
        }
    }
    
    private function validateEmail($field, $value, $param, $data) {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->errors[$field][] = "The {$field} must be a valid email address.";
        }
    }
    
    private function validateMin($field, $value, $param, $data) {
        if (strlen($value) < $param) {
            $this->errors[$field][] = "The {$field} must be at least {$param} characters.";
        }
    }
    
    private function validateMax($field, $value, $param, $data) {
        if (strlen($value) > $param) {
            $this->errors[$field][] = "The {$field} may not be greater than {$param} characters.";
        }
    }
    
    private function validateAlpha($field, $value, $param, $data) {
        if (!ctype_alpha(str_replace(' ', '', $value))) {
            $this->errors[$field][] = "The {$field} may only contain letters.";
        }
    }
    
    private function validateAlphaNum($field, $value, $param, $data) {
        if (!ctype_alnum(str_replace(' ', '', $value))) {
            $this->errors[$field][] = "The {$field} may only contain letters and numbers.";
        }
    }
    
    private function validateSame($field, $value, $param, $data) {
        if (!isset($data[$param]) || $value !== $data[$param]) {
            $this->errors[$field][] = "The {$field} and {$param} must match.";
        }
    }
    
    private function validateIn($field, $value, $param, $data) {
        $allowedValues = explode(',', $param);
        if (!in_array($value, $allowedValues)) {
            $this->errors[$field][] = "The selected {$field} is invalid.";
        }
    }
    
    private function validateInteger($field, $value, $param, $data) {
        if (!filter_var($value, FILTER_VALIDATE_INT)) {
            $this->errors[$field][] = "The {$field} must be an integer.";
        }
    }
    
    private function validateRequiredWith($field, $value, $param, $data) {
        if (isset($data[$param]) && !empty($data[$param]) && (!isset($data[$field]) || empty($data[$field]))) {
            $this->errors[$field][] = "The {$field} field is required when {$param} is present.";
        }
    }
    
    // إضافة في نهاية الملف قبل الأقواس الأخيرة
    private function validateUrl($field, $value, $param, $data) {
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            $this->errors[$field][] = "The {$field} must be a valid URL.";
        }
    }
    
    private function validateDate($field, $value, $param, $data) {
        $date = DateTime::createFromFormat($param ?: 'Y-m-d', $value);
        if (!$date || $date->format($param ?: 'Y-m-d') !== $value) {
            $this->errors[$field][] = "The {$field} must be a valid date in format {$param}.";
        }
    }
    
    private function validateNumeric($field, $value, $param, $data) {
        if (!is_numeric($value)) {
            $this->errors[$field][] = "The {$field} must be numeric.";
        }
    }
}