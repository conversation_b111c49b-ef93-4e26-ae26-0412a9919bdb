<?php

require_once __DIR__ . '/../config/database.php';

class Award {
    private $db;
    private $table = 'awards';
    private $nomineesTable = 'award_nominees';
    private $votesTable = 'award_votes';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function getActiveAwards() {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE is_active = TRUE ORDER BY year DESC");
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function getNominees($awardId) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->nomineesTable} WHERE award_id = :award_id ORDER BY votes_count DESC");
        $stmt->bindParam(':award_id', $awardId, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function findNomineeById($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->nomineesTable} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function hasVoted($awardId, $email, $ipAddress) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM {$this->votesTable} 
            WHERE award_id = :award_id AND (email = :email OR ip_address = :ip_address)");
        $stmt->bindParam(':award_id', $awardId, PDO::PARAM_INT);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':ip_address', $ipAddress, PDO::PARAM_STR);
        $stmt->execute();
        
        return $stmt->fetchColumn() > 0;
    }
    
    public function vote($data) {
        $stmt = $this->db->prepare("INSERT INTO {$this->votesTable} 
            (award_id, nominee_id, user_id, email, ip_address) 
            VALUES 
            (:award_id, :nominee_id, :user_id, :email, :ip_address)");
        
        $stmt->bindParam(':award_id', $data['award_id'], PDO::PARAM_INT);
        $stmt->bindParam(':nominee_id', $data['nominee_id'], PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':ip_address', $data['ip_address'], PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function incrementVotes($nomineeId) {
        $stmt = $this->db->prepare("UPDATE {$this->nomineesTable} 
            SET votes_count = votes_count + 1 
            WHERE id = :nominee_id");
        $stmt->bindParam(':nominee_id', $nomineeId, PDO::PARAM_INT);
        
        return $stmt->execute();
    }
    
    public function getResults($awardId) {
        $stmt = $this->db->prepare("SELECT n.*, 
            (SELECT COUNT(*) FROM {$this->votesTable} WHERE nominee_id = n.id) as vote_count 
            FROM {$this->nomineesTable} n 
            WHERE n.award_id = :award_id 
            ORDER BY vote_count DESC");
        $stmt->bindParam(':award_id', $awardId, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function create($data) {
        $stmt = $this->db->prepare("INSERT INTO {$this->table} 
            (title, description, year, is_active) 
            VALUES 
            (:title, :description, :year, :is_active)");
        
        $stmt->bindParam(':title', $data['title'], PDO::PARAM_STR);
        $stmt->bindParam(':description', $data['description'], PDO::PARAM_STR);
        $stmt->bindParam(':year', $data['year'], PDO::PARAM_INT);
        $stmt->bindParam(':is_active', $data['is_active'] ?? true, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function createNominee($data) {
        $stmt = $this->db->prepare("INSERT INTO {$this->nomineesTable} 
            (award_id, nominee_name, nominee_description, photo) 
            VALUES 
            (:award_id, :nominee_name, :nominee_description, :photo)");
        
        $stmt->bindParam(':award_id', $data['award_id'], PDO::PARAM_INT);
        $stmt->bindParam(':nominee_name', $data['nominee_name'], PDO::PARAM_STR);
        $stmt->bindParam(':nominee_description', $data['nominee_description'], PDO::PARAM_STR);
        $stmt->bindParam(':photo', $data['photo'] ?? null, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function update($id, $data) {
        // Start building the query
        $query = "UPDATE {$this->table} SET ";
        $updateFields = [];
        $params = [':id' => $id];
        
        // Add fields to update
        foreach ($data as $key => $value) {
            $updateFields[] = "{$key} = :{$key}";
            $params[":{$key}"] = $value;
        }
        
        // Add updated_at timestamp
        $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
        
        // Complete the query
        $query .= implode(", ", $updateFields) . " WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        return $stmt->execute();
    }
    
    public function updateNominee($id, $data) {
        // Start building the query
        $query = "UPDATE {$this->nomineesTable} SET ";
        $updateFields = [];
        $params = [':id' => $id];
        
        // Add fields to update
        foreach ($data as $key => $value) {
            $updateFields[] = "{$key} = :{$key}";
            $params[":{$key}"] = $value;
        }
        
        // Add updated_at timestamp
        $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
        
        // Complete the query
        $query .= implode(", ", $updateFields) . " WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        return $stmt->execute();
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    public function deleteNominee($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->nomineesTable} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
}