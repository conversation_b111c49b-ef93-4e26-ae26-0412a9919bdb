<?php

require_once __DIR__ . '/../config/database.php';

class Message {
    private $db;
    private $table = 'messages';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function getAll($options = []) {
        $query = "SELECT * FROM {$this->table}";
        $params = [];
        
        // Add filters if provided
        if (!empty($options['filters'])) {
            $whereConditions = [];
            
            if (isset($options['filters']['is_read'])) {
                $whereConditions[] = "is_read = :is_read";
                $params[':is_read'] = $options['filters']['is_read'];
            }
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
        }
        
        // Add ordering
        $query .= " ORDER BY " . ($options['order_by'] ?? 'created_at') . " " . ($options['order_direction'] ?? 'DESC');
        
        // Add limit and offset for pagination
        if (isset($options['limit'])) {
            $query .= " LIMIT :limit";
            $params[':limit'] = $options['limit'];
            
            if (isset($options['offset'])) {
                $query .= " OFFSET :offset";
                $params[':offset'] = $options['offset'];
            }
        }
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function create($data) {
        $stmt = $this->db->prepare("INSERT INTO {$this->table} 
            (name, email, subject, message, ip_address) 
            VALUES 
            (:name, :email, :subject, :message, :ip_address)");
        
        $stmt->bindParam(':name', $data['name'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':subject', $data['subject'], PDO::PARAM_STR);
        $stmt->bindParam(':message', $data['message'], PDO::PARAM_STR);
        $stmt->bindParam(':ip_address', $data['ip_address'] ?? $_SERVER['REMOTE_ADDR'], PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function markAsRead($id) {
        $stmt = $this->db->prepare("UPDATE {$this->table} SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
}