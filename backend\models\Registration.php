<?php

require_once __DIR__ . '/../config/database.php';

class Registration {
    private $db;
    private $table = 'registrations';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function findByEmail($email) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE email = :email");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function findByConfirmationCode($code) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE confirmation_code = :code");
        $stmt->bindParam(':code', $code, PDO::PARAM_STR);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function getAll($options = []) {
        $query = "SELECT * FROM {$this->table}";
        $params = [];
        
        // Add filters if provided
        if (!empty($options['filters'])) {
            $whereConditions = [];
            
            if (isset($options['filters']['registration_type'])) {
                $whereConditions[] = "registration_type = :registration_type";
                $params[':registration_type'] = $options['filters']['registration_type'];
            }
            
            if (isset($options['filters']['payment_status'])) {
                $whereConditions[] = "payment_status = :payment_status";
                $params[':payment_status'] = $options['filters']['payment_status'];
            }
            
            if (isset($options['filters']['is_confirmed'])) {
                $whereConditions[] = "is_confirmed = :is_confirmed";
                $params[':is_confirmed'] = $options['filters']['is_confirmed'];
            }
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
        }
        
        // Add ordering
        $query .= " ORDER BY " . ($options['order_by'] ?? 'registration_date') . " " . ($options['order_direction'] ?? 'DESC');
        
        // Add limit and offset for pagination
        if (isset($options['limit'])) {
            $query .= " LIMIT :limit";
            $params[':limit'] = $options['limit'];
            
            if (isset($options['offset'])) {
                $query .= " OFFSET :offset";
                $params[':offset'] = $options['offset'];
            }
        }
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function create($data) {
        // Generate confirmation code
        $data['confirmation_code'] = bin2hex(random_bytes(32));
        
        $stmt = $this->db->prepare("INSERT INTO {$this->table} 
            (user_id, first_name, last_name, email, phone, organization, position, 
            registration_type, payment_status, payment_method, payment_reference, 
            amount, is_confirmed, confirmation_code, notes) 
            VALUES 
            (:user_id, :first_name, :last_name, :email, :phone, :organization, :position, 
            :registration_type, :payment_status, :payment_method, :payment_reference, 
            :amount, :is_confirmed, :confirmation_code, :notes)");
        
        $stmt->bindParam(':user_id', $data['user_id'] ?? null, $data['user_id'] ? PDO::PARAM_INT : PDO::PARAM_NULL);
        $stmt->bindParam(':first_name', $data['first_name'], PDO::PARAM_STR);
        $stmt->bindParam(':last_name', $data['last_name'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':phone', $data['phone'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':organization', $data['organization'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':position', $data['position'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':registration_type', $data['registration_type'], PDO::PARAM_STR);
        $stmt->bindParam(':payment_status', $data['payment_status'] ?? 'pending', PDO::PARAM_STR);
        $stmt->bindParam(':payment_method', $data['payment_method'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':payment_reference', $data['payment_reference'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':amount', $data['amount'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':is_confirmed', $data['is_confirmed'] ?? false, PDO::PARAM_BOOL);
        $stmt->bindParam(':confirmation_code', $data['confirmation_code'], PDO::PARAM_STR);
        $stmt->bindParam(':notes', $data['notes'] ?? null, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function update($id, $data) {
        // Start building the query
        $query = "UPDATE {$this->table} SET ";
        $updateFields = [];
        $params = [':id' => $id];
        
        // Add fields to update
        foreach ($data as $key => $value) {
            $updateFields[] = "{$key} = :{$key}";
            $params[":$key"] = $value;
        }
        
        // Add updated_at timestamp
        $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
        
        // Complete the query
        $query .= implode(", ", $updateFields) . " WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        return $stmt->execute();
    }
    
    public function confirm($id) {
        $stmt = $this->db->prepare("UPDATE {$this->table} SET is_confirmed = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    public function updatePaymentStatus($id, $status, $reference = null) {
        $query = "UPDATE {$this->table} SET payment_status = :status, updated_at = CURRENT_TIMESTAMP";
        $params = [':id' => $id, ':status' => $status];
        
        if ($reference) {
            $query .= ", payment_reference = :reference";
            $params[':reference'] = $reference;
        }
        
        $query .= " WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        return $stmt->execute();
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
}