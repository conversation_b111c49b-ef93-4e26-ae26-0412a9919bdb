<?php

require_once __DIR__ . '/../config/database.php';

class Settings {
    private $db;
    private $table = 'settings';
    private $cache = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadCache();
    }
    
    private function loadCache() {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table}");
        $stmt->execute();
        
        $settings = $stmt->fetchAll();
        
        foreach ($settings as $setting) {
            $this->cache[$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'is_public' => $setting['is_public']
            ];
        }
    }
    
    public function get($key, $default = null) {
        if (isset($this->cache[$key])) {
            return $this->cache[$key]['value'];
        }
        
        $stmt = $this->db->prepare("SELECT setting_value FROM {$this->table} WHERE setting_key = :key");
        $stmt->bindParam(':key', $key, PDO::PARAM_STR);
        $stmt->execute();
        
        $result = $stmt->fetch();
        
        if ($result) {
            $this->cache[$key] = [
                'value' => $result['setting_value'],
                'is_public' => false // Default to false until we know
            ];
            return $result['setting_value'];
        }
        
        return $default;
    }
    
    public function getByKey($key) {
        return $this->get($key);
    }
    
    public function getAllPublic() {
        $publicSettings = [];
        
        foreach ($this->cache as $key => $data) {
            if ($data['is_public']) {
                $publicSettings[$key] = $data['value'];
            }
        }
        
        if (empty($publicSettings)) {
            $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM {$this->table} WHERE is_public = TRUE");
            $stmt->execute();
            
            $settings = $stmt->fetchAll();
            
            foreach ($settings as $setting) {
                $publicSettings[$setting['setting_key']] = $setting['setting_value'];
            }
        }
        
        return $publicSettings;
    }
    
    public function getAll() {
        $allSettings = [];
        
        foreach ($this->cache as $key => $data) {
            $allSettings[$key] = $data['value'];
        }
        
        if (empty($allSettings)) {
            $stmt = $this->db->prepare("SELECT setting_key, setting_value FROM {$this->table}");
            $stmt->execute();
            
            $settings = $stmt->fetchAll();
            
            foreach ($settings as $setting) {
                $allSettings[$setting['setting_key']] = $setting['setting_value'];
            }
        }
        
        return $allSettings;
    }
    
    public function set($key, $value, $isPublic = false) {
        // Check if setting exists
        $stmt = $this->db->prepare("SELECT id FROM {$this->table} WHERE setting_key = :key");
        $stmt->bindParam(':key', $key, PDO::PARAM_STR);
        $stmt->execute();
        
        $exists = $stmt->fetch();
        
        if ($exists) {
            // Update existing setting
            $stmt = $this->db->prepare("UPDATE {$this->table} SET setting_value = :value, is_public = :is_public, updated_at = CURRENT_TIMESTAMP WHERE setting_key = :key");
        } else {
            // Create new setting
            $stmt = $this->db->prepare("INSERT INTO {$this->table} (setting_key, setting_value, is_public) VALUES (:key, :value, :is_public)");
        }
        
        $stmt->bindParam(':key', $key, PDO::PARAM_STR);
        $stmt->bindParam(':value', $value, PDO::PARAM_STR);
        $stmt->bindParam(':is_public', $isPublic, PDO::PARAM_BOOL);
        
        $result = $stmt->execute();
        
        if ($result) {
            // Update cache
            $this->cache[$key] = [
                'value' => $value,
                'is_public' => $isPublic
            ];
        }
        
        return $result;
    }
    
    public function delete($key) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE setting_key = :key");
        $stmt->bindParam(':key', $key, PDO::PARAM_STR);
        
        $result = $stmt->execute();
        
        if ($result && isset($this->cache[$key])) {
            unset($this->cache[$key]);
        }
        
        return $result;
    }
}