<?php

require_once __DIR__ . '/../config/database.php';

class Speaker {
    private $db;
    private $table = 'speakers';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function findByEmail($email) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE email = :email");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function getAll($options = []) {
        $query = "SELECT * FROM {$this->table}";
        $params = [];
        
        // Add filters if provided
        if (!empty($options['filters'])) {
            $whereConditions = [];
            
            if (isset($options['filters']['is_keynote'])) {
                $whereConditions[] = "is_keynote = :is_keynote";
                $params[':is_keynote'] = $options['filters']['is_keynote'];
            }
            
            if (isset($options['filters']['is_featured'])) {
                $whereConditions[] = "is_featured = :is_featured";
                $params[':is_featured'] = $options['filters']['is_featured'];
            }
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
        }
        
        // Add ordering
        $query .= " ORDER BY " . ($options['order_by'] ?? 'last_name') . " " . ($options['order_direction'] ?? 'ASC');
        
        // Add limit and offset for pagination
        if (isset($options['limit'])) {
            $query .= " LIMIT :limit";
            $params[':limit'] = $options['limit'];
            
            if (isset($options['offset'])) {
                $query .= " OFFSET :offset";
                $params[':offset'] = $options['offset'];
            }
        }
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function create($data) {
        $stmt = $this->db->prepare("INSERT INTO {$this->table} 
            (first_name, last_name, email, bio, photo, organization, position, 
            social_linkedin, social_twitter, presentation_title, presentation_abstract, 
            is_keynote, is_featured) 
            VALUES 
            (:first_name, :last_name, :email, :bio, :photo, :organization, :position, 
            :social_linkedin, :social_twitter, :presentation_title, :presentation_abstract, 
            :is_keynote, :is_featured)");
        
        $stmt->bindParam(':first_name', $data['first_name'], PDO::PARAM_STR);
        $stmt->bindParam(':last_name', $data['last_name'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':bio', $data['bio'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':photo', $data['photo'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':organization', $data['organization'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':position', $data['position'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':social_linkedin', $data['social_linkedin'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':social_twitter', $data['social_twitter'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':presentation_title', $data['presentation_title'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':presentation_abstract', $data['presentation_abstract'] ?? null, PDO::PARAM_STR);
        $stmt->bindParam(':is_keynote', $data['is_keynote'] ?? false, PDO::PARAM_BOOL);
        $stmt->bindParam(':is_featured', $data['is_featured'] ?? false, PDO::PARAM_BOOL);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function update($id, $data) {
        // Start building the query
        $query = "UPDATE {$this->table} SET ";
        $updateFields = [];
        $params = [':id' => $id];
        
        // Add fields to update
        foreach ($data as $key => $value) {
            $updateFields[] = "{$key} = :{$key}";
            $params[":$key"] = $value;
        }
        
        // Add updated_at timestamp
        $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
        
        // Complete the query
        $query .= implode(", ", $updateFields) . " WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        return $stmt->execute();
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
}