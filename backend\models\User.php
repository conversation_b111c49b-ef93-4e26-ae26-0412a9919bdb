<?php

require_once __DIR__ . '/../config/database.php';

class User {
    private $db;
    private $table = 'users';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT id, username, email, first_name, last_name, role, is_active, created_at, updated_at, last_login FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function findByEmail($email) {
        $stmt = $this->db->prepare("SELECT id, username, email, password, first_name, last_name, role, is_active, created_at, updated_at, last_login FROM {$this->table} WHERE email = :email");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function findByUsername($username) {
        $stmt = $this->db->prepare("SELECT id, username, email, password, first_name, last_name, role, is_active, created_at, updated_at, last_login FROM {$this->table} WHERE username = :username");
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function create($data) {
        // Hash password using Argon2id
        $data['password'] = password_hash($data['password'], PASSWORD_ARGON2ID);
        
        $stmt = $this->db->prepare("INSERT INTO {$this->table} (username, email, password, first_name, last_name, role) VALUES (:username, :email, :password, :first_name, :last_name, :role)");
        
        $stmt->bindParam(':username', $data['username'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':password', $data['password'], PDO::PARAM_STR);
        $stmt->bindParam(':first_name', $data['first_name'], PDO::PARAM_STR);
        $stmt->bindParam(':last_name', $data['last_name'], PDO::PARAM_STR);
        $stmt->bindParam(':role', $data['role'] ?? 'user', PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function update($id, $data) {
        // Start building the query
        $query = "UPDATE {$this->table} SET ";
        $updateFields = [];
        $params = [':id' => $id];
        
        // Add fields to update
        foreach ($data as $key => $value) {
            // Skip password as it needs special handling
            if ($key === 'password') continue;
            
            $updateFields[] = "{$key} = :{$key}";
            $params[":$key"] = $value;
        }
        
        // Handle password separately if it exists
        if (isset($data['password']) && !empty($data['password'])) {
            $updateFields[] = "password = :password";
            $params[':password'] = password_hash($data['password'], PASSWORD_ARGON2ID);
        }
        
        // Add updated_at timestamp
        $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
        
        // Complete the query
        $query .= implode(", ", $updateFields) . " WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        
        foreach ($params as $param => $value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($param, $value, $type);
        }
        
        return $stmt->execute();
    }
    
    public function updateLastLogin($id) {
        $stmt = $this->db->prepare("UPDATE {$this->table} SET last_login = CURRENT_TIMESTAMP WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        return $stmt->execute();
    }
    
    public function verifyPassword($password, $hashedPassword) {
        return password_verify($password, $hashedPassword);
    }
}