<?php

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        putenv("$name=$value");
        $_ENV[$name] = $value;
        $_SERVER[$name] = $value;
    }
}

// Register error handler
require_once __DIR__ . '/../helpers/error_handler.php';
ErrorHandler::register();

// Set error reporting based on environment
$debug = filter_var(getenv('APP_DEBUG') ?: false, FILTER_VALIDATE_BOOLEAN);
if ($debug) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set('UTC');

// Enable CORS
$allowedOrigins = getenv('CORS_ALLOWED_ORIGINS') ?: 'http://localhost:3000';
if ($allowedOrigins === '*') {
    header('Access-Control-Allow-Origin: *');
} else {
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    if (in_array($origin, explode(',', $allowedOrigins))) {
        header('Access-Control-Allow-Origin: ' . $origin);
    }
}
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit();
}

// Parse request URI
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = '/api';

// Check if request is for API
if (strpos($requestUri, $basePath) !== 0) {
    header('HTTP/1.1 404 Not Found');
    echo json_encode(['status' => 'error', 'message' => 'Not Found']);
    exit();
}

// Remove base path from request URI
$requestUri = substr($requestUri, strlen($basePath));

// Parse request method and body
$method = $_SERVER['REQUEST_METHOD'];
$data = [];

if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true) ?: [];
}

// Initialize Security class
require_once __DIR__ . '/../helpers/security.php';
Security::init();

// Initialize CSRF protection
require_once __DIR__ . '/../helpers/csrf.php';

// Check for JWT token in Authorization header
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($authHeader, 'Bearer ') === 0) {
        $token = substr($authHeader, 7);
    }
}

// Route API requests
$routes = [
    // Auth routes
    '/auth/login' => ['POST' => ['AuthController', 'login']],
    '/auth/logout' => ['POST' => ['AuthController', 'logout']],
    
    // Registration routes
    '/register/submit' => ['POST' => ['RegisterController', 'submit']],
    '/register/confirm' => ['GET' => ['RegisterController', 'confirm']],
    
    // Speaker routes
    '/speakers/list' => ['GET' => ['SpeakerController', 'getSpeakers']],
    
    // Contact routes
    '/contact/send' => ['POST' => ['ContactController', 'send']],
    
    // Award routes
    '/award/list' => ['GET' => ['AwardController', 'getAwards']],
    '/award/vote' => ['POST' => ['AwardController', 'vote']],
    
    // i18n routes
    '/i18n/{lang}' => ['GET' => ['I18nController', 'getTranslations']]
];

// Find matching route
$matchedRoute = null;
$routeParams = [];

foreach ($routes as $route => $handlers) {
    // Convert route pattern to regex
    $pattern = preg_replace('/{([^}]+)}/', '(?P<\\1>[^/]+)', $route);
    $pattern = '#^' . $pattern . '$#';
    
    if (preg_match($pattern, $requestUri, $matches)) {
        $matchedRoute = $route;
        
        // Extract route parameters
        foreach ($matches as $key => $value) {
            if (is_string($key)) {
                $routeParams[$key] = $value;
            }
        }
        
        break;
    }
}

// Handle route
if ($matchedRoute && isset($routes[$matchedRoute][$method])) {
    list($controllerName, $methodName) = $routes[$matchedRoute][$method];
    
    // Load controller
    require_once __DIR__ . "/../controllers/{$controllerName}.php";
    $controller = new $controllerName();
    
    // Apply CSRF protection for non-GET requests
    if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'OPTIONS'])) {
        if (!CSRF::validateRequest()) {
            echo Response::error('CSRF token validation failed', [], 403);
            exit();
        }
    }
    
    // Apply rate limiting
    require_once __DIR__ . '/../helpers/rate_limiter.php';
    $rateLimiter = new RateLimiter();
    $rateLimiter->check();
    
    // Call controller method with data and route parameters
    $result = $controller->$methodName($data, $routeParams);
    
    // Output result
    echo $result;
} else {
    // Route not found
    header('HTTP/1.1 404 Not Found');
    echo json_encode(['status' => 'error', 'message' => 'Route not found']);
}