-- Create database if not exists
CREATE DATABASE IF NOT EXISTS irego_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE irego_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('admin', 'user', 'editor', 'moderator') NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    failed_login_attempts TINYINT UNSIGNED DEFAULT 0,
    last_failed_login TIMES<PERSON>MP NULL,
    password_reset_token VARCHAR(100) NULL,
    password_reset_expires TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_username (username),
    CONSTRAINT chk_email CHECK (email REGEXP '^[^@]+@[^@]+\.[^@]+$')
) ENGINE=InnoDB;

-- Speakers table
CREATE TABLE IF NOT EXISTS speakers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    bio TEXT,
    photo VARCHAR(255),
    organization VARCHAR(100),
    position VARCHAR(100),
    social_linkedin VARCHAR(255),
    social_twitter VARCHAR(255),
    presentation_title VARCHAR(255),
    presentation_abstract TEXT,
    is_keynote BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_speaker_email (email)
) ENGINE=InnoDB;

-- Registrations table
CREATE TABLE IF NOT EXISTS registrations (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    organization VARCHAR(100),
    position VARCHAR(100),
    registration_type ENUM('regular', 'student', 'speaker', 'sponsor', 'vip') NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    amount DECIMAL(10,2),
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_confirmed BOOLEAN DEFAULT FALSE,
    confirmation_code VARCHAR(64),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_registration_email (email),
    INDEX idx_registration_confirmation (confirmation_code),
    INDEX idx_registration_type (registration_type),
    INDEX idx_payment_status (payment_status),
    CONSTRAINT chk_reg_email CHECK (email REGEXP '^[^@]+@[^@]+\.[^@]+$')
) ENGINE=InnoDB;

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_message_email (email),
    INDEX idx_message_read (is_read)
) ENGINE=InnoDB;

-- Awards table
CREATE TABLE IF NOT EXISTS awards (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    year INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_award_year (year),
    INDEX idx_award_active (is_active)
) ENGINE=InnoDB;

-- Award nominees table
CREATE TABLE IF NOT EXISTS award_nominees (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    award_id INT UNSIGNED NOT NULL,
    nominee_name VARCHAR(100) NOT NULL,
    nominee_description TEXT,
    photo VARCHAR(255),
    votes_count INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (award_id) REFERENCES awards(id) ON DELETE CASCADE,
    INDEX idx_nominee_award (award_id)
) ENGINE=InnoDB;

-- Award votes table
CREATE TABLE IF NOT EXISTS award_votes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    award_id INT UNSIGNED NOT NULL,
    nominee_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    email VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (award_id) REFERENCES awards(id) ON DELETE CASCADE,
    FOREIGN KEY (nominee_id) REFERENCES award_nominees(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_vote (award_id, email),
    UNIQUE KEY unique_vote_ip (award_id, ip_address),
    INDEX idx_vote_nominee (nominee_id),
    INDEX idx_vote_email (email)
) ENGINE=InnoDB;

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_setting_public (is_public)
) ENGINE=InnoDB;

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, is_public) VALUES
('site_name', 'IREGO Conference', TRUE),
('site_description', 'International Renewable Energy Governance Conference', TRUE),
('contact_email', '<EMAIL>', TRUE),
('registration_open', 'true', TRUE),
('conference_start_date', '2023-10-15', TRUE),
('conference_end_date', '2023-10-17', TRUE),
('conference_venue', 'Tripoli International Conference Center', TRUE),
('max_registrations', '500', FALSE),
('enable_awards_voting', 'true', TRUE);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$argon2id$v=19$m=65536,t=4,p=1$ZVlVeE9UWmhOV1UzTmpJMw$TkjIpWOIELHHkSxK2Jl6jKIlQdxf4+/XMidC7+rpzQA', 'Admin', 'User', 'admin');


-- إضافة جدول سجل التدقيق
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT UNSIGNED,
    old_values TEXT,
    new_values TEXT,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_table (table_name),
    INDEX idx_audit_created (created_at)
) ENGINE=InnoDB;


-- إضافة جدول محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent VARCHAR(255),
    success BOOLEAN DEFAULT FALSE,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_login_email (email),
    INDEX idx_login_ip (ip_address),
    INDEX idx_login_time (attempt_time)
) ENGINE=InnoDB;


-- إضافة مشغل لتتبع تغييرات المستخدمين
DELIMITER //
CREATE TRIGGER users_after_update
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values)
    VALUES (
        @current_user_id, -- يجب تعيين هذا المتغير في التطبيق قبل التحديث
        'UPDATE',
        'users',
        NEW.id,
        JSON_OBJECT(
            'username', OLD.username,
            'email', OLD.email,
            'first_name', OLD.first_name,
            'last_name', OLD.last_name,
            'role', OLD.role,
            'is_active', OLD.is_active
        ),
        JSON_OBJECT(
            'username', NEW.username,
            'email', NEW.email,
            'first_name', NEW.first_name,
            'last_name', NEW.last_name,
            'role', NEW.role,
            'is_active', NEW.is_active
        )
    );
END //
DELIMITER ;