<?php

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../helpers/db_backup.php';

class DBManager {
    private $db;
    private $backup;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->backup = new DBBackup();
    }
    
    public function runMaintenance() {
        $this->optimizeTables();
        $this->analyzeTablesStatus();
        $this->checkForErrors();
        $this->createBackup();
    }
    
    public function optimizeTables() {
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            $this->db->execute("OPTIMIZE TABLE {$table}");
            echo "Optimized table: {$table}\n";
        }
    }
    
    public function analyzeTablesStatus() {
        $tables = $this->getTables();
        $issues = [];
        
        foreach ($tables as $table) {
            $result = $this->db->query("ANALYZE TABLE {$table}");
            $status = $result->fetch();
            
            if ($status['Msg_type'] !== 'status' || $status['Msg_text'] !== 'OK') {
                $issues[] = "Issue with table {$table}: {$status['Msg_text']}";
            }
        }
        
        if (!empty($issues)) {
            echo "Found issues with tables:\n";
            foreach ($issues as $issue) {
                echo "- {$issue}\n";
            }
        } else {
            echo "All tables are OK\n";
        }
    }
    
    public function checkForErrors() {
        $tables = $this->getTables();
        $errors = [];
        
        foreach ($tables as $table) {
            $result = $this->db->query("CHECK TABLE {$table}");
            $status = $result->fetch();
            
            if ($status['Msg_type'] === 'error') {
                $errors[] = "Error in table {$table}: {$status['Msg_text']}";
            }
        }
        
        if (!empty($errors)) {
            echo "Found errors in tables:\n";
            foreach ($errors as $error) {
                echo "- {$error}\n";
            }
        } else {
            echo "No errors found in tables\n";
        }
    }
    
    public function createBackup() {
        try {
            $backupFile = $this->backup->createBackup();
            echo "Backup created successfully: {$backupFile}\n";
        } catch (Exception $e) {
            echo "Backup failed: {$e->getMessage()}\n";
        }
    }
    
    public function restoreBackup($backupFile) {
        try {
            $this->backup->restoreBackup($backupFile);
            echo "Backup restored successfully\n";
        } catch (Exception $e) {
            echo "Restore failed: {$e->getMessage()}\n";
        }
    }
    
    public function checkHealth() {
        $health = [
            'connection' => $this->db->checkConnection(),
            'server_info' => $this->db->getServerInfo(),
            'tables_count' => count($this->getTables()),
            'backups' => $this->backup->getBackupsList()
        ];
        
        echo "Database Health Check:\n";
        echo "- Connection: " . ($health['connection'] ? 'OK' : 'FAILED') . "\n";
        echo "- MySQL Version: {$health['server_info']['version']}\n";
        echo "- Tables Count: {$health['tables_count']}\n";
        echo "- Available Backups: " . count($health['backups']) . "\n";
        
        return $health;
    }
    
    private function getTables() {
        $result = $this->db->query("SHOW TABLES");
        $tables = [];
        
        while ($row = $result->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        return $tables;
    }
    
    public static function run($command, $args = []) {
        $manager = new self();
        
        switch ($command) {
            case 'maintenance':
                $manager->runMaintenance();
                break;
            case 'optimize':
                $manager->optimizeTables();
                break;
            case 'analyze':
                $manager->analyzeTablesStatus();
                break;
            case 'check':
                $manager->checkForErrors();
                break;
            case 'backup':
                $manager->createBackup();
                break;
            case 'restore':
                if (empty($args[0])) {
                    echo "Error: Backup file path is required\n";
                    exit(1);
                }
                $manager->restoreBackup($args[0]);
                break;
            case 'health':
                $manager->checkHealth();
                break;
            default:
                echo "Unknown command: {$command}\n";
                echo "Available commands: maintenance, optimize, analyze, check, backup, restore, health\n";
                exit(1);
        }
    }
}

// تشغيل السكريبت من سطر الأوامر
if (php_sapi_name() === 'cli' && isset($argv[1])) {
    DBManager::run($argv[1], array_slice($argv, 2));
}